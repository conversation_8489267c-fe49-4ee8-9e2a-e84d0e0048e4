# WorkFlo Backend - Django REST API

A comprehensive Human Resource Management System (HRMS) backend built with Django REST Framework, designed to handle all aspects of employee management, payroll, attendance, and performance tracking.

## 🚀 Features

### Core Modules

1. **Authentication & User Management**
   - Custom User model with role-based access control
   - JWT token authentication
   - Password reset functionality
   - Session management

2. **Employee Management**
   - Employee profiles with comprehensive information
   - Department management with hierarchy
   - Salary profiles and bank information
   - Emergency contacts

3. **Attendance & Time Tracking**
   - Daily attendance records
   - BioStar integration for biometric devices
   - Break time tracking
   - Overtime calculation

4. **Leave Management**
   - Multiple leave types
   - Leave balance tracking
   - Leave application workflow
   - Company holidays management

5. **Payroll System**
   - Kenyan tax calculation (PAYE)
   - Statutory deductions (NSSF, NHIF, Housing Levy)
   - Pay cycle management
   - Payroll adjustments and bonuses

6. **Performance Management**
   - Performance review templates
   - Employee performance reviews
   - Goal setting and tracking

7. **Overtime Management**
   - Overtime request workflow
   - Multiple overtime types
   - Approval system
   - Rate calculations

## 🛠 Technology Stack

- **Backend Framework**: Django 5.2
- **API Framework**: Django REST Framework 3.15.2
- **Authentication**: JWT (djangorestframework-simplejwt)
- **Database**: SQLite (development) / PostgreSQL (production)
- **Python Version**: 3.10+

## 📋 Prerequisites

- Python 3.10 or higher
- pip (Python package installer)
- Virtual environment (recommended)

## 🔧 Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd workflo_back
```

### 2. Create Virtual Environment
```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Environment Configuration
```bash
cp .env.example .env
# Edit .env file with your configuration
```

### 5. Database Setup
```bash
python manage.py makemigrations
python manage.py migrate
```

### 6. Create Superuser
```bash
python manage.py createsuperuser
```

### 7. Run Development Server
```bash
python manage.py runserver
```

The API will be available at `http://127.0.0.1:8000/`

## 📚 API Documentation

### Authentication Endpoints
- `POST /auth/login/` - User login (JWT token)
- `POST /auth/refresh/` - Refresh JWT token

### Core API Endpoints
- `GET|POST /api/users/` - User management
- `GET|POST /api/departments/` - Department management
- `GET|POST /api/employee-profiles/` - Employee profiles
- `GET|POST /api/salary-profiles/` - Salary information
- `GET|POST /api/attendance-records/` - Attendance tracking
- `GET|POST /api/leave-applications/` - Leave management
- `GET|POST /api/payroll-records/` - Payroll data
- `GET|POST /api/performance-reviews/` - Performance management

### Admin Interface
Access the Django admin at `http://127.0.0.1:8000/admin/`

## 🏗 Project Structure

```
workflo_back/
├── core/                          # Main application
│   ├── models/                    # Database models (organized by module)
│   │   ├── auth.py               # User authentication models
│   │   ├── employees.py          # Employee-related models
│   │   ├── attendance.py         # Attendance tracking models
│   │   ├── leave.py              # Leave management models
│   │   ├── payroll.py            # Payroll system models
│   │   ├── performance.py        # Performance management models
│   │   ├── overtime.py           # Overtime management models
│   │   └── ...                   # Other modules
│   ├── views/                     # API views (business logic)
│   ├── serializers/               # DRF serializers
│   ├── migrations/                # Database migrations
│   ├── admin.py                   # Django admin configuration
│   └── urls.py                    # URL routing
├── workflo_back/                  # Project settings
├── requirements.txt               # Python dependencies
├── manage.py                      # Django management script
└── README.md                      # This file
```

## 🔐 User Roles & Permissions

1. **Admin**: Full system access
2. **HR**: Employee and leave management
3. **Supervisor**: Department-level access
4. **Accountant**: Payroll and financial data
5. **Employee**: Personal data access only

## 💰 Kenyan Payroll Features

- **Tax Brackets**: Implements 2024 Kenyan PAYE rates
- **NSSF**: Tiered contribution calculation
- **NHIF/SHA**: 2.75% deduction
- **Housing Levy**: 1.5% deduction
- **16 Major Banks**: Support for Kenyan banking system

## 🔄 Business Logic

All business logic is implemented in **views**, not models, following Django best practices:
- User permission checks
- Salary calculations
- Leave balance management
- Attendance processing
- Payroll generation

## 🧪 Testing

```bash
# Run tests
python manage.py test

# Run with coverage
pip install coverage
coverage run --source='.' manage.py test
coverage report
```

## 🚀 Deployment

### Production Settings
1. Set `DEBUG=False` in environment
2. Configure PostgreSQL database
3. Set up proper CORS origins
4. Configure email backend
5. Use production WSGI server (Gunicorn)

### Environment Variables
```bash
DEBUG=False
SECRET_KEY=your-secret-key
DB_NAME=workflo_production
DB_USER=postgres
DB_PASSWORD=your-password
DB_HOST=localhost
DB_PORT=5432
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🤝 Support

For support and questions, please contact the development team.

---

**WorkFlo Backend** - Comprehensive HRMS Solution for Modern Businesses
