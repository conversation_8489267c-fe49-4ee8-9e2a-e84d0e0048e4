# Django Settings
DEBUG=True
SECRET_KEY=django-insecure-)jjp%dfddm@57tc^w%vk0r(5gf=m0v&(ep%j)#s0_ir#dgmj=)

# Database Configuration
DB_NAME=workflo_db
DB_USER=postgres
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=5432

# Email Configuration (for notifications)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379/0

# File Upload Settings
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes

# Security Settings
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
