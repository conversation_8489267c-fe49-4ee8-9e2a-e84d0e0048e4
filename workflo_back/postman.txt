# WORKFLO BACKEND API TESTING GUIDE
# Comprehensive Postman Collection for all 58 API endpoints
# Base URL: http://127.0.0.1:8000

## AUTHENTICATION ENDPOINTS

### 1. <PERSON>gin (JWT <PERSON>ken)
POST /auth/login/
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "admin123"
}

### 2. Refresh Token
POST /auth/refresh/
Content-Type: application/json

{
    "refresh": "{{refresh_token}}"
}

### 3. User Registration
POST /auth/register/
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "newpassword123",
    "first_name": "New",
    "last_name": "User",
    "employee_id": "EMP006",
    "role": "employee"
}

## CORE API ENDPOINTS (58 Total)

### AUTHENTICATION & USER MANAGEMENT (3 endpoints)

#### 1. Users API
GET /api/users/
Authorization: Bearer {{access_token}}

POST /api/users/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "email": "<EMAIL>",
    "first_name": "<PERSON>",
    "last_name": "Doe",
    "employee_id": "EMP007",
    "phone_number": "+254712345678",
    "role": "employee",
    "is_active": true
}

#### 2. User Sessions API
GET /api/user-sessions/
Authorization: Bearer {{access_token}}

#### 3. Password Reset Tokens API
GET /api/password-reset-tokens/
Authorization: Bearer {{access_token}}

### ORGANIZATIONAL STRUCTURE (1 endpoint)

#### 4. Departments API
GET /api/departments/
Authorization: Bearer {{access_token}}

POST /api/departments/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "name": "Marketing",
    "description": "Marketing and Communications Department",
    "supervisor_id": 2,
    "budget": 500000.00,
    "location": "Floor 3, Building A"
}

### EMPLOYEE INFORMATION (4 endpoints)

#### 5. Employee Profiles API
GET /api/employee-profiles/
Authorization: Bearer {{access_token}}

POST /api/employee-profiles/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "user_id": 6,
    "department_id": 1,
    "job_title": "Software Developer",
    "hire_date": "2024-01-15",
    "employment_type": "full_time",
    "work_location": "office",
    "supervisor_id": 3,
    "nssf_number": "NSSF123456",
    "nhif_number": "NHIF789012",
    "kra_pin": "A123456789Z",
    "national_id": "12345678",
    "date_of_birth": "1990-05-15",
    "gender": "male",
    "marital_status": "single",
    "nationality": "Kenyan",
    "address": "123 Main Street, Nairobi",
    "city": "Nairobi",
    "state": "Nairobi County",
    "postal_code": "00100",
    "status": "active"
}

#### 6. Salary Profiles API
GET /api/salary-profiles/
Authorization: Bearer {{access_token}}

POST /api/salary-profiles/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "employee_id": 6,
    "basic_salary": 80000.00,
    "hourly_rate": 500.00,
    "currency": "KSH",
    "pay_frequency": "monthly",
    "allowances": 15000.00,
    "overtime_rate": 750.00,
    "commission_rate": 2.5,
    "effective_from": "2024-01-15",
    "is_active": true
}

#### 7. Bank Profiles API
GET /api/bank-profiles/
Authorization: Bearer {{access_token}}

POST /api/bank-profiles/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "employee_id": 6,
    "bank_name": "Kenya Commercial Bank",
    "bank_code": "01",
    "branch_name": "Nairobi Branch",
    "branch_code": "001",
    "account_number": "**********",
    "account_name": "John Doe",
    "account_type": "savings",
    "is_primary": true,
    "is_active": true
}

#### 8. Emergency Contacts API
GET /api/emergency-contacts/
Authorization: Bearer {{access_token}}

POST /api/emergency-contacts/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "employee_id": 6,
    "contact_name": "Jane Doe",
    "relationship": "Spouse",
    "phone_number": "+************",
    "email": "<EMAIL>",
    "address": "123 Main Street, Nairobi",
    "priority_order": 1,
    "is_active": true
}

### ATTENDANCE & TIME TRACKING (9 endpoints)

#### 9. Overtime Types API
GET /api/overtime-types/
Authorization: Bearer {{access_token}}

POST /api/overtime-types/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "name": "Weekend Overtime",
    "description": "Overtime work during weekends",
    "rate_multiplier": 2.0,
    "max_hours_per_day": 8.0,
    "max_hours_per_week": 16.0,
    "max_hours_per_month": 64.0,
    "requires_pre_approval": true,
    "auto_approve_threshold": 4.0,
    "is_active": true
}

#### 10. Overtime Requests API
GET /api/overtime-requests/
Authorization: Bearer {{access_token}}

POST /api/overtime-requests/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "overtime_type_id": 1,
    "request_date": "2024-06-01",
    "planned_start_time": "18:00:00",
    "planned_end_time": "22:00:00",
    "planned_hours": 4.0,
    "reason": "Project deadline",
    "justification": "Critical project delivery",
    "project_code": "PROJ001"
}

#### 11. Overtime Records API
GET /api/overtime-records/
Authorization: Bearer {{access_token}}

POST /api/overtime-records/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "employee_id": 6,
    "overtime_type_id": 1,
    "overtime_date": "2024-06-01",
    "start_time": "2024-06-01T18:00:00Z",
    "end_time": "2024-06-01T22:00:00Z",
    "total_hours": 4.0,
    "rate_multiplier": 1.5,
    "regular_hourly_rate": 500.00,
    "currency": "KSH",
    "overtime_category": "weekday",
    "is_pre_approved": true
}

#### 12. Overtime Approval Workflows API
GET /api/overtime-approval-workflows/
Authorization: Bearer {{access_token}}

#### 13. Overtime Budgets API
GET /api/overtime-budgets/
Authorization: Bearer {{access_token}}

#### 14. Overtime Calculations API
GET /api/overtime-calculations/
Authorization: Bearer {{access_token}}

#### 15. Attendance Records API
GET /api/attendance-records/
Authorization: Bearer {{access_token}}

POST /api/attendance-records/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "employee_id": 6,
    "date": "2024-06-01",
    "check_in": "2024-06-01T08:00:00Z",
    "check_out": "2024-06-01T17:00:00Z",
    "total_hours": 8.0,
    "regular_hours": 8.0,
    "overtime_hours": 0.0,
    "status": "present"
}

#### 16. BioStar Events API
GET /api/biostar-events/
Authorization: Bearer {{access_token}}

#### 17. BioStar Devices API
GET /api/biostar-devices/
Authorization: Bearer {{access_token}}

### LEAVE MANAGEMENT (4 endpoints)

#### 18. Leave Types API
GET /api/leave-types/
Authorization: Bearer {{access_token}}

POST /api/leave-types/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "name": "Compassionate Leave",
    "description": "Leave for family emergencies",
    "max_days_per_year": 5,
    "carry_forward_allowed": false,
    "requires_approval": true,
    "is_paid": true,
    "is_active": true
}

#### 19. Leave Balances API
GET /api/leave-balances/
Authorization: Bearer {{access_token}}

#### 20. Leave Applications API
GET /api/leave-applications/
Authorization: Bearer {{access_token}}

POST /api/leave-applications/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "leave_type_id": 1,
    "start_date": "2024-07-01",
    "end_date": "2024-07-05",
    "days_requested": 5.0,
    "reason": "Family vacation",
    "status": "pending"
}

#### 21. Company Holidays API
GET /api/company-holidays/
Authorization: Bearer {{access_token}}

POST /api/company-holidays/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "name": "Independence Day",
    "date": "2024-12-12",
    "description": "Kenya Independence Day",
    "is_recurring": true,
    "applies_to_all": true
}

### PAYROLL SYSTEM (5 endpoints)

#### 22. Pay Cycles API
GET /api/pay-cycles/
Authorization: Bearer {{access_token}}

POST /api/pay-cycles/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "pay_period": "June 2024",
    "start_date": "2024-06-01",
    "end_date": "2024-06-30",
    "pay_date": "2024-07-05",
    "status": "draft"
}

#### 23. Payroll Records API
GET /api/payroll-records/
Authorization: Bearer {{access_token}}

#### 24. Payroll Adjustments API
GET /api/payroll-adjustments/
Authorization: Bearer {{access_token}}

#### 25. Salary Adjustments API
GET /api/salary-adjustments/
Authorization: Bearer {{access_token}}

#### 26. Employee Benefits API
GET /api/employee-benefits/
Authorization: Bearer {{access_token}}

POST /api/employee-benefits/
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
    "employee_id": 6,
    "benefit_type": "health_insurance",
    "benefit_name": "Medical Cover",
    "description": "Comprehensive medical insurance",
    "value_type": "coverage",
    "value": 100000.00,
    "currency": "KSH",
    "provider": "AAR Insurance",
    "start_date": "2024-01-15",
    "status": "active",
    "employee_contribution": 2000.00,
    "employer_contribution": 8000.00,
    "dependents_covered": 3
}

## SAMPLE DATA FOR TESTING

### Test Users
1. <EMAIL> / admin123 (Admin)
2. <EMAIL> / password123 (HR)
3. <EMAIL> / password123 (Supervisor)
4. <EMAIL> / password123 (Accountant)
5. <EMAIL> / password123 (Employee)

### Test Departments
1. Human Resources
2. Information Technology
3. Finance & Accounting
4. Sales & Marketing
5. Operations

### Test Leave Types
1. Annual Leave (21 days)
2. Sick Leave (14 days)
3. Maternity Leave (90 days)
4. Paternity Leave (14 days)
5. Emergency Leave (3 days)

## ENVIRONMENT VARIABLES FOR POSTMAN

```json
{
    "base_url": "http://127.0.0.1:8000",
    "access_token": "",
    "refresh_token": "",
    "user_id": "",
    "employee_id": ""
}
```

## TESTING WORKFLOW

1. **Authentication**: Login to get access token
2. **Set Token**: Use access token in Authorization header
3. **CRUD Operations**: Test Create, Read, Update, Delete for each endpoint
4. **Role-based Testing**: Test with different user roles
5. **Error Handling**: Test invalid data and unauthorized access
6. **Pagination**: Test list endpoints with pagination
7. **Filtering**: Test search and filter functionality
8. **Business Logic**: Test approval workflows and calculations

## RESPONSE FORMATS

### Success Response (200/201)
```json
{
    "id": 1,
    "field1": "value1",
    "field2": "value2",
    "created_at": "2024-06-01T10:00:00Z",
    "updated_at": "2024-06-01T10:00:00Z"
}
```

### Error Response (400/401/403/404)
```json
{
    "error": "Error message",
    "details": {
        "field": ["Field error message"]
    }
}
```

### Paginated Response
```json
{
    "count": 100,
    "next": "http://127.0.0.1:8000/api/endpoint/?page=2",
    "previous": null,
    "results": [...]
}
```
