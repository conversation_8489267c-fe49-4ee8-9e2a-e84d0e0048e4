from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models.auth import User, UserSession, PasswordResetToken
from .models.organization import Department
from .models.employees import EmployeeProfile, SalaryProfile, BankProfile, EmergencyContact
from .models.attendance import AttendanceRecord, BiostarEvent, BiostarDevice
from .models.leave import LeaveType, LeaveBalance, LeaveApplication, CompanyHoliday
from .models.payroll import PayCycle, PayrollRecord, PayrollAdjustment, SalaryAdjustment
from .models.performance import PerformanceReviewTemplate, PerformanceReview, PerformanceGoal


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Custom User admin"""
    list_display = ['email', 'first_name', 'last_name', 'employee_id', 'role', 'is_active', 'date_joined']
    list_filter = ['role', 'is_active', 'is_deleted', 'date_joined']
    search_fields = ['email', 'first_name', 'last_name', 'employee_id']
    ordering = ['email']

    fieldsets = BaseUserAdmin.fieldsets + (
        ('Employee Information', {
            'fields': ('employee_id', 'phone_number', 'profile_picture', 'role')
        }),
        ('Audit Information', {
            'fields': ('is_deleted', 'created_by', 'updated_by', 'deleted_by', 'deleted_at')
        }),
    )

    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('Employee Information', {
            'fields': ('employee_id', 'phone_number', 'role')
        }),
    )


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    """Department admin"""
    list_display = ['name', 'supervisor', 'parent_department', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    raw_id_fields = ['supervisor', 'parent_department']


@admin.register(EmployeeProfile)
class EmployeeProfileAdmin(admin.ModelAdmin):
    """Employee Profile admin"""
    list_display = ['user', 'job_title', 'department', 'employment_type', 'hire_date', 'status']
    list_filter = ['employment_type', 'work_location', 'status', 'hire_date']
    search_fields = ['user__first_name', 'user__last_name', 'user__email', 'job_title']
    raw_id_fields = ['user', 'department', 'supervisor']


@admin.register(SalaryProfile)
class SalaryProfileAdmin(admin.ModelAdmin):
    """Salary Profile admin"""
    list_display = ['employee', 'basic_salary', 'currency', 'pay_frequency', 'effective_from', 'is_active']
    list_filter = ['currency', 'pay_frequency', 'is_active', 'effective_from']
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__email']
    raw_id_fields = ['employee']


@admin.register(AttendanceRecord)
class AttendanceRecordAdmin(admin.ModelAdmin):
    """Attendance Record admin"""
    list_display = ['employee', 'date', 'check_in', 'check_out', 'total_hours', 'status']
    list_filter = ['status', 'date', 'biostar_synced']
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__email']
    raw_id_fields = ['employee']
    date_hierarchy = 'date'


@admin.register(LeaveApplication)
class LeaveApplicationAdmin(admin.ModelAdmin):
    """Leave Application admin"""
    list_display = ['employee', 'leave_type', 'start_date', 'end_date', 'days_requested', 'status']
    list_filter = ['status', 'leave_type', 'start_date']
    search_fields = ['employee__first_name', 'employee__last_name', 'reason']
    raw_id_fields = ['employee', 'approved_by']
    date_hierarchy = 'start_date'


@admin.register(PayCycle)
class PayCycleAdmin(admin.ModelAdmin):
    """Pay Cycle admin"""
    list_display = ['pay_period', 'start_date', 'end_date', 'pay_date', 'status', 'total_employees']
    list_filter = ['status', 'start_date']
    search_fields = ['pay_period']
    date_hierarchy = 'start_date'


# Register remaining models with basic admin
admin.site.register(UserSession)
admin.site.register(PasswordResetToken)
admin.site.register(BankProfile)
admin.site.register(EmergencyContact)
admin.site.register(BiostarEvent)
admin.site.register(BiostarDevice)
admin.site.register(LeaveType)
admin.site.register(LeaveBalance)
admin.site.register(CompanyHoliday)
admin.site.register(PayrollRecord)
admin.site.register(PayrollAdjustment)
admin.site.register(SalaryAdjustment)
admin.site.register(PerformanceReviewTemplate)
admin.site.register(PerformanceReview)
admin.site.register(PerformanceGoal)
