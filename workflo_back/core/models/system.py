# System & Audit Models
# Based on system modules from database.txt

from django.db import models
from django.utils import timezone as django_timezone
from .auth import User

# Placeholder for system models
# Will be implemented with full CRUD operations

class AuditLog(models.Model):
    """Audit logs placeholder"""
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    action = models.CharField(max_length=50)
    table_name = models.CharField(max_length=100)
    record_id = models.IntegerField(null=True, blank=True)
    old_values = models.JSONField(null=True, blank=True)
    new_values = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(default=django_timezone.now)
    
    class Meta:
        db_table = 'audit_logs'
    
    def __str__(self):
        return f"{self.user} - {self.action} - {self.table_name}"


class ActivityLog(models.Model):
    """Activity logs placeholder"""
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    activity_type = models.CharField(max_length=50)
    activity_description = models.TextField()
    module = models.CharField(max_length=50, blank=True, null=True)
    created_at = models.DateTimeField(default=django_timezone.now)
    
    class Meta:
        db_table = 'activity_logs'
    
    def __str__(self):
        return f"{self.user} - {self.activity_type}"


class SystemSetting(models.Model):
    """System settings placeholder"""
    setting_key = models.CharField(max_length=100, unique=True)
    setting_value = models.TextField(blank=True, null=True)
    setting_type = models.CharField(max_length=20, default='string')
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=django_timezone.now)
    
    class Meta:
        db_table = 'system_settings'
    
    def __str__(self):
        return self.setting_key


class CompanyInfo(models.Model):
    """Company information placeholder"""
    company_name = models.CharField(max_length=255)
    address = models.TextField(blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    timezone = models.CharField(max_length=50, default='Africa/Nairobi')
    currency = models.CharField(max_length=3, default='KSH')
    created_at = models.DateTimeField(default=django_timezone.now)
    
    class Meta:
        db_table = 'company_info'
    
    def __str__(self):
        return self.company_name
