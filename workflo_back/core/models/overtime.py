from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from .auth import User
from .organization import Department
from .attendance import AttendanceRecord


class OvertimeType(models.Model):
    """
    Overtime types and policies
    Based on overtime_types table from database.txt
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    rate_multiplier = models.DecimalField(max_digits=3, decimal_places=2, default=1.5)  # e.g., 1.5 for time-and-a-half
    max_hours_per_day = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    max_hours_per_week = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    max_hours_per_month = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    requires_pre_approval = models.BooleanField(default=True)
    auto_approve_threshold = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)  # Auto-approve if under this many hours
    is_active = models.BooleanField(default=True)
    
    # Audit fields
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_overtime_types')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_overtime_types')
    
    class Meta:
        db_table = 'overtime_types'
        verbose_name = 'Overtime Type'
        verbose_name_plural = 'Overtime Types'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.rate_multiplier}x)"


class OvertimeRequest(models.Model):
    """
    Overtime requests (pre-approval system)
    Based on overtime_requests table from database.txt
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('cancelled', 'Cancelled'),
        ('completed', 'Completed'),
    ]
    
    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='overtime_requests')
    overtime_type = models.ForeignKey(OvertimeType, on_delete=models.CASCADE, related_name='requests')
    
    # Request details
    request_date = models.DateField()
    planned_start_time = models.TimeField()
    planned_end_time = models.TimeField()
    planned_hours = models.DecimalField(max_digits=4, decimal_places=2)
    reason = models.TextField()
    justification = models.TextField(blank=True, null=True)
    project_code = models.CharField(max_length=50, blank=True, null=True)
    department_approval_required = models.BooleanField(default=True)
    
    # Approval workflow
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    requested_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='requested_overtime')
    supervisor_approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='supervisor_approved_overtime')
    supervisor_approved_at = models.DateTimeField(null=True, blank=True)
    supervisor_comments = models.TextField(blank=True, null=True)
    admin_approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='admin_approved_overtime')
    admin_approved_at = models.DateTimeField(null=True, blank=True)
    admin_comments = models.TextField(blank=True, null=True)
    
    # Final approval
    final_approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='final_approved_overtime')
    final_approved_at = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True, null=True)
    
    # Completion tracking
    actual_start_time = models.DateTimeField(null=True, blank=True)
    actual_end_time = models.DateTimeField(null=True, blank=True)
    actual_hours = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    completion_notes = models.TextField(blank=True, null=True)
    completed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='completed_overtime')
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Audit fields
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_overtime_requests')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_overtime_requests')
    
    class Meta:
        db_table = 'overtime_requests'
        verbose_name = 'Overtime Request'
        verbose_name_plural = 'Overtime Requests'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.employee.first_name} {self.employee.last_name} - {self.request_date} ({self.planned_hours}h)"
    
    def calculate_planned_hours(self):
        """Calculate planned hours from start and end times"""
        from datetime import datetime, timedelta
        
        # Convert time objects to datetime for calculation
        start_datetime = datetime.combine(self.request_date, self.planned_start_time)
        end_datetime = datetime.combine(self.request_date, self.planned_end_time)
        
        # Handle overnight shifts
        if end_datetime <= start_datetime:
            end_datetime += timedelta(days=1)
        
        duration = end_datetime - start_datetime
        self.planned_hours = duration.total_seconds() / 3600
        return self.planned_hours


class OvertimeRecord(models.Model):
    """
    Overtime records (actual overtime worked)
    Based on overtime_records table from database.txt
    """
    OVERTIME_CATEGORY_CHOICES = [
        ('weekday', 'Weekday'),
        ('weekend', 'Weekend'),
        ('holiday', 'Holiday'),
        ('emergency', 'Emergency'),
        ('project_deadline', 'Project Deadline'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('paid', 'Paid'),
    ]

    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='overtime_records')
    overtime_request = models.ForeignKey(OvertimeRequest, on_delete=models.SET_NULL, null=True, blank=True, related_name='records')
    overtime_type = models.ForeignKey(OvertimeType, on_delete=models.CASCADE, related_name='records')
    attendance_record = models.ForeignKey(AttendanceRecord, on_delete=models.SET_NULL, null=True, blank=True, related_name='overtime_records')

    # Overtime details
    overtime_date = models.DateField()
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    total_hours = models.DecimalField(max_digits=4, decimal_places=2)
    rate_multiplier = models.DecimalField(max_digits=3, decimal_places=2)

    # Calculation details
    regular_hourly_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    overtime_hourly_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    currency = models.CharField(max_length=3, default='KSH')

    # Classification
    overtime_category = models.CharField(max_length=30, choices=OVERTIME_CATEGORY_CHOICES, null=True, blank=True)
    is_emergency = models.BooleanField(default=False)
    is_pre_approved = models.BooleanField(default=False)

    # Approval status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_overtime_records')
    approved_at = models.DateTimeField(null=True, blank=True)
    approval_comments = models.TextField(blank=True, null=True)

    # Payroll integration
    pay_cycle = models.ForeignKey('PayCycle', on_delete=models.SET_NULL, null=True, blank=True, related_name='overtime_records')
    included_in_payroll = models.BooleanField(default=False)
    payroll_processed_at = models.DateTimeField(null=True, blank=True)

    # Audit fields
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_overtime_records')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_overtime_records')

    class Meta:
        db_table = 'overtime_records'
        verbose_name = 'Overtime Record'
        verbose_name_plural = 'Overtime Records'
        unique_together = ['employee', 'overtime_date', 'start_time']
        ordering = ['-overtime_date', '-start_time']

    def __str__(self):
        return f"{self.employee.first_name} {self.employee.last_name} - {self.overtime_date} ({self.total_hours}h)"

    def calculate_amount(self):
        """Calculate overtime amount based on hourly rate and multiplier"""
        if self.regular_hourly_rate and self.rate_multiplier:
            self.overtime_hourly_rate = self.regular_hourly_rate * self.rate_multiplier
            self.total_amount = self.overtime_hourly_rate * self.total_hours
            self.save()
        return self.total_amount
