"""
Training & Development Views
Comprehensive ViewSets for training-related operations
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from drf_spectacular.utils import extend_schema, extend_schema_view
import logging

from ..models.training import (
    TrainingModule, TrainingVenue, EmployeeTrainingAssignment,
    TrainingSession, TrainingSessionParticipant
)
from ..serializers.training import (
    TrainingModuleSerializer, TrainingVenueSerializer, EmployeeTrainingAssignmentSerializer,
    TrainingSessionSerializer, TrainingSessionParticipantSerializer
)
from ..permissions import IsHROrAdmin

logger = logging.getLogger(__name__)


@extend_schema_view(
    list=extend_schema(description="List training modules"),
    create=extend_schema(description="Create new training module"),
    retrieve=extend_schema(description="Get training module details"),
    update=extend_schema(description="Update training module"),
    destroy=extend_schema(description="Delete training module"),
)
class TrainingModuleViewSet(viewsets.ModelViewSet):
    """ViewSet for managing training modules"""
    queryset = TrainingModule.objects.all()
    serializer_class = TrainingModuleSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['difficulty_level', 'category', 'is_mandatory', 'is_active']
    search_fields = ['title', 'description', 'instructor_name']
    ordering_fields = ['title', 'duration_hours', 'created_at']
    ordering = ['-created_at']

    @action(detail=False, methods=['get'])
    def mandatory(self, request):
        """Get mandatory training modules"""
        mandatory_modules = self.queryset.filter(is_mandatory=True, is_active=True)
        serializer = self.get_serializer(mandatory_modules, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_category(self, request):
        """Get training modules grouped by category"""
        categories = self.queryset.values_list('category', flat=True).distinct()
        result = {}
        for category in categories:
            if category:
                modules = self.queryset.filter(category=category, is_active=True)
                result[category] = self.get_serializer(modules, many=True).data
        return Response(result)


@extend_schema_view(
    list=extend_schema(description="List training venues"),
    create=extend_schema(description="Create new training venue"),
    retrieve=extend_schema(description="Get training venue details"),
    update=extend_schema(description="Update training venue"),
    destroy=extend_schema(description="Delete training venue"),
)
class TrainingVenueViewSet(viewsets.ModelViewSet):
    """ViewSet for managing training venues"""
    queryset = TrainingVenue.objects.all()
    serializer_class = TrainingVenueSerializer
    permission_classes = [permissions.IsAuthenticated, IsHROrAdmin]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'capacity']
    search_fields = ['name', 'location', 'address']
    ordering_fields = ['name', 'capacity', 'hourly_rate']
    ordering = ['name']

    @action(detail=False, methods=['get'])
    def available(self, request):
        """Get available training venues"""
        available_venues = self.queryset.filter(status='available')
        serializer = self.get_serializer(available_venues, many=True)
        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(description="List employee training assignments"),
    create=extend_schema(description="Create new training assignment"),
    retrieve=extend_schema(description="Get training assignment details"),
    update=extend_schema(description="Update training assignment"),
    destroy=extend_schema(description="Delete training assignment"),
)
class EmployeeTrainingAssignmentViewSet(viewsets.ModelViewSet):
    """ViewSet for managing employee training assignments"""
    queryset = EmployeeTrainingAssignment.objects.all()
    serializer_class = EmployeeTrainingAssignmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'training_module', 'assigned_by']
    search_fields = ['employee__first_name', 'employee__last_name', 'training_module__title']
    ordering_fields = ['assigned_date', 'due_date', 'progress_percentage']
    ordering = ['-assigned_date']

    def get_queryset(self):
        """Filter queryset based on user role"""
        user = self.request.user
        if user.role in ['admin', 'hr']:
            return EmployeeTrainingAssignment.objects.all()
        elif user.role == 'supervisor':
            return EmployeeTrainingAssignment.objects.filter(
                employee__employee_profile__department__supervisor=user
            )
        else:
            return EmployeeTrainingAssignment.objects.filter(employee=user)

    @action(detail=True, methods=['post'])
    def mark_completed(self, request, pk=None):
        """Mark training assignment as completed"""
        assignment = self.get_object()
        assignment.status = 'completed'
        assignment.progress_percentage = 100
        assignment.completion_date = timezone.now()
        assignment.score = request.data.get('score')
        assignment.feedback = request.data.get('feedback', '')
        assignment.save()
        
        logger.info(f"Training assignment {pk} marked as completed")
        return Response({'status': 'completed'})

    @action(detail=False, methods=['get'])
    def my_assignments(self, request):
        """Get current user's training assignments"""
        assignments = EmployeeTrainingAssignment.objects.filter(employee=request.user)
        serializer = self.get_serializer(assignments, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue training assignments"""
        from django.utils import timezone
        overdue = self.get_queryset().filter(
            due_date__lt=timezone.now().date(),
            status__in=['assigned', 'in_progress']
        )
        serializer = self.get_serializer(overdue, many=True)
        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(description="List training sessions"),
    create=extend_schema(description="Create new training session"),
    retrieve=extend_schema(description="Get training session details"),
    update=extend_schema(description="Update training session"),
    destroy=extend_schema(description="Delete training session"),
)
class TrainingSessionViewSet(viewsets.ModelViewSet):
    """ViewSet for managing training sessions"""
    queryset = TrainingSession.objects.all()
    serializer_class = TrainingSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'training_module', 'venue', 'instructor']
    search_fields = ['title', 'training_module__title']
    ordering_fields = ['start_datetime', 'end_datetime', 'current_participants']
    ordering = ['-start_datetime']

    @action(detail=True, methods=['post'])
    def register(self, request, pk=None):
        """Register for a training session"""
        session = self.get_object()
        
        # Check if session is full
        if session.max_participants and session.current_participants >= session.max_participants:
            return Response(
                {'error': 'Training session is full'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if already registered
        if TrainingSessionParticipant.objects.filter(session=session, employee=request.user).exists():
            return Response(
                {'error': 'Already registered for this session'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Register participant
        participant = TrainingSessionParticipant.objects.create(
            session=session,
            employee=request.user,
            attendance_status='registered'
        )
        
        # Update participant count
        session.current_participants += 1
        session.save()
        
        logger.info(f"User {request.user} registered for training session {pk}")
        return Response({'status': 'registered'})

    @action(detail=True, methods=['post'])
    def unregister(self, request, pk=None):
        """Unregister from a training session"""
        session = self.get_object()
        
        try:
            participant = TrainingSessionParticipant.objects.get(
                session=session,
                employee=request.user
            )
            participant.attendance_status = 'cancelled'
            participant.save()
            
            # Update participant count
            session.current_participants = max(0, session.current_participants - 1)
            session.save()
            
            logger.info(f"User {request.user} unregistered from training session {pk}")
            return Response({'status': 'unregistered'})
            
        except TrainingSessionParticipant.DoesNotExist:
            return Response(
                {'error': 'Not registered for this session'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """Get upcoming training sessions"""
        from django.utils import timezone
        upcoming = self.queryset.filter(
            start_datetime__gt=timezone.now(),
            status='scheduled'
        )
        serializer = self.get_serializer(upcoming, many=True)
        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(description="List training session participants"),
    create=extend_schema(description="Create new participant record"),
    retrieve=extend_schema(description="Get participant details"),
    update=extend_schema(description="Update participant record"),
    destroy=extend_schema(description="Delete participant record"),
)
class TrainingSessionParticipantViewSet(viewsets.ModelViewSet):
    """ViewSet for managing training session participants"""
    queryset = TrainingSessionParticipant.objects.all()
    serializer_class = TrainingSessionParticipantSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['attendance_status', 'completion_status', 'certificate_issued']
    search_fields = ['employee__first_name', 'employee__last_name', 'session__title']
    ordering_fields = ['registered_at', 'score']
    ordering = ['-registered_at']

    def get_queryset(self):
        """Filter queryset based on user role"""
        user = self.request.user
        if user.role in ['admin', 'hr']:
            return TrainingSessionParticipant.objects.all()
        elif user.role == 'supervisor':
            return TrainingSessionParticipant.objects.filter(
                employee__employee_profile__department__supervisor=user
            )
        else:
            return TrainingSessionParticipant.objects.filter(employee=user)

    @action(detail=True, methods=['post'], permission_classes=[IsHROrAdmin])
    def mark_attended(self, request, pk=None):
        """Mark participant as attended"""
        participant = self.get_object()
        participant.attendance_status = 'attended'
        participant.completion_status = 'completed'
        participant.save()
        
        logger.info(f"Participant {pk} marked as attended")
        return Response({'status': 'attended'})

    @action(detail=True, methods=['post'], permission_classes=[IsHROrAdmin])
    def issue_certificate(self, request, pk=None):
        """Issue certificate to participant"""
        participant = self.get_object()
        if participant.completion_status == 'completed':
            participant.certificate_issued = True
            participant.save()
            
            logger.info(f"Certificate issued to participant {pk}")
            return Response({'status': 'certificate_issued'})
        else:
            return Response(
                {'error': 'Training not completed'},
                status=status.HTTP_400_BAD_REQUEST
            )
