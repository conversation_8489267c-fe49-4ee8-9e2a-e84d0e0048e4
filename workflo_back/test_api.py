#!/usr/bin/env python3
"""
Simple API test script for WorkFlo Backend
Tests basic authentication and API endpoints
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:8000"
LOGIN_URL = f"{BASE_URL}/auth/login/"
USERS_URL = f"{BASE_URL}/api/users/"

# Test credentials (using the superuser we created)
TEST_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "admin123"
}

def test_authentication():
    """Test JWT authentication"""
    print("🔐 Testing Authentication...")
    
    response = requests.post(LOGIN_URL, json=TEST_CREDENTIALS)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Authentication successful!")
        print(f"   Access Token: {data['access'][:50]}...")
        print(f"   User Info: {data['user']['first_name']} {data['user']['last_name']}")
        return data['access']
    else:
        print(f"❌ Authentication failed: {response.status_code}")
        print(f"   Response: {response.text}")
        return None

def test_users_api(token):
    """Test Users API endpoint"""
    print("\n👥 Testing Users API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(USERS_URL, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Users API working!")
        print(f"   Found {len(data['results'])} users")
        for user in data['results']:
            print(f"   - {user['first_name']} {user['last_name']} ({user['role']})")
    else:
        print(f"❌ Users API failed: {response.status_code}")
        print(f"   Response: {response.text}")

def test_departments_api(token):
    """Test Departments API endpoint"""
    print("\n🏢 Testing Departments API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    departments_url = f"{BASE_URL}/api/departments/"
    response = requests.get(departments_url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Departments API working!")
        print(f"   Found {len(data['results'])} departments")
    else:
        print(f"❌ Departments API failed: {response.status_code}")

def main():
    """Main test function"""
    print("🚀 WorkFlo Backend API Test")
    print("=" * 40)
    
    # Test authentication
    token = test_authentication()
    
    if token:
        # Test API endpoints
        test_users_api(token)
        test_departments_api(token)
        
        print("\n" + "=" * 40)
        print("✅ All tests completed!")
        print("🎉 WorkFlo Backend is working correctly!")
    else:
        print("\n❌ Cannot proceed with API tests due to authentication failure")

if __name__ == "__main__":
    main()
