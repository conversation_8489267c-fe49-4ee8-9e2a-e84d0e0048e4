INFO 2025-05-31 21:27:45,128 autoreload 41513 128464055791616 Watching for file changes with StatReloader
INFO 2025-05-31 21:27:55,334 autoreload 41513 128464055791616 /home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/overtime_views.py changed, reloading.
INFO 2025-05-31 21:27:56,596 autoreload 41602 123914895634432 Watching for file changes with StatReloader
INFO 2025-05-31 21:28:43,811 autoreload 41602 123914895634432 /home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/overtime_views.py changed, reloading.
INFO 2025-05-31 21:28:45,142 autoreload 42074 139002434973696 Watching for file changes with StatReloader
INFO 2025-05-31 21:30:15,528 autoreload 42903 139612998795264 Watching for file changes with StatReloader
INFO 2025-05-31 21:30:43,467 basehttp 42903 *************** "GET /api/docs/ HTTP/1.1" 200 4714
ERROR 2025-05-31 21:30:48,548 log 42903 *************** Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 104, in get_operation
    auth = self.get_auth()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 364, in get_auth
    perms = [p.__class__ for p in self.view.get_permissions()]
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/payroll_views.py", line 23, in get_permissions
    if hasattr(self.request, 'user') and self.request.user.role not in ['admin', 'hr', 'accountant']:
AttributeError: 'AnonymousUser' object has no attribute 'role'
ERROR 2025-05-31 21:30:48,551 basehttp 42903 *************** "GET /api/schema/ HTTP/1.1" 500 140498
ERROR 2025-05-31 21:30:48,962 log 42903 *************** Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 104, in get_operation
    auth = self.get_auth()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 364, in get_auth
    perms = [p.__class__ for p in self.view.get_permissions()]
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/payroll_views.py", line 23, in get_permissions
    if hasattr(self.request, 'user') and self.request.user.role not in ['admin', 'hr', 'accountant']:
AttributeError: 'AnonymousUser' object has no attribute 'role'
ERROR 2025-05-31 21:30:48,963 basehttp 42903 *************** "GET /api/schema/ HTTP/1.1" 500 140498
WARNING 2025-05-31 21:30:50,820 log 42903 *************** Unauthorized: /api/system/health/
WARNING 2025-05-31 21:30:50,820 basehttp 42903 *************** "GET /api/system/health/ HTTP/1.1" 401 172
INFO 2025-05-31 21:30:59,771 basehttp 42903 *************** "POST /auth/login/ HTTP/1.1" 200 796
INFO 2025-05-31 21:31:09,266 monitoring 42903 *************** Health check <NAME_EMAIL>, status: warning
INFO 2025-05-31 21:31:09,267 basehttp 42903 *************** "GET /api/system/health/ HTTP/1.1" 200 975
INFO 2025-05-31 21:31:15,131 basehttp 42903 *************** "GET /api/docs/ HTTP/1.1" 200 4714
ERROR 2025-05-31 21:31:16,107 log 42903 *************** Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 104, in get_operation
    auth = self.get_auth()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 364, in get_auth
    perms = [p.__class__ for p in self.view.get_permissions()]
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/payroll_views.py", line 23, in get_permissions
    if hasattr(self.request, 'user') and self.request.user.role not in ['admin', 'hr', 'accountant']:
AttributeError: 'AnonymousUser' object has no attribute 'role'
ERROR 2025-05-31 21:31:16,109 basehttp 42903 *************** "GET /api/schema/ HTTP/1.1" 500 140498
ERROR 2025-05-31 21:31:16,676 log 42903 *************** Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 104, in get_operation
    auth = self.get_auth()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 364, in get_auth
    perms = [p.__class__ for p in self.view.get_permissions()]
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/payroll_views.py", line 23, in get_permissions
    if hasattr(self.request, 'user') and self.request.user.role not in ['admin', 'hr', 'accountant']:
AttributeError: 'AnonymousUser' object has no attribute 'role'
ERROR 2025-05-31 21:31:16,677 basehttp 42903 *************** "GET /api/schema/ HTTP/1.1" 500 140498
