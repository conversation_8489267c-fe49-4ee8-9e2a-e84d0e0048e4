# WORKFLO-BACK DATABASE TABLES
# Complete list of all tables from database.txt schema
# Total Tables: 50

## 1. AUTHENTICATION & USER MANAGEMENT (3 tables)
1. users
2. user_sessions
3. password_reset_tokens

## 2. ORGANIZATIONAL STRUCTURE (1 table)
4. departments

## 3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> INFORMATION (4 tables)
5. employee_profiles
6. salary_profiles
7. bank_profiles
8. emergency_contacts

## 4. ATTENDANCE & TIME TRACKING (9 tables)
9. overtime_types
10. overtime_requests
11. overtime_records
12. overtime_approval_workflows
13. overtime_budgets
14. overtime_calculations
15. attendance_records
16. biostar_events
17. biostar_devices

## 5. LEAVE MANAGEMENT (4 tables)
18. leave_types
19. leave_balances
20. leave_applications
21. company_holidays

## 6. PAYROLL SYSTEM (4 tables)
22. pay_cycles
23. payroll_records
24. payroll_adjustments
25. salary_adjustments
26. employee_benefits

## 7. PERFORMANCE MANAGEMENT (3 tables)
27. performance_review_templates
28. performance_reviews
29. performance_goals

## 8. TRAINING & DEVELOPMENT (5 tables)
30. training_modules
31. training_venues
32. employee_training_assignments
33. training_sessions
34. training_session_participants

## 9. RECRUITMENT & JOB MANAGEMENT (4 tables)
35. job_postings
36. job_applications
37. interview_schedules
38. candidate_evaluations

## 10. DOCUMENT MANAGEMENT (4 tables)
39. document_categories
40. employee_documents
41. company_documents
42. document_acknowledgments

## 11. NOTIFICATIONS & COMMUNICATION (3 tables)
43. notification_templates
44. notifications
45. email_logs

## 12. AUDIT LOGS & SYSTEM TRACKING (4 tables)
46. audit_logs
47. activity_logs
48. system_settings
49. company_info

## 13. EMPLOYEE ENGAGEMENT & WELLNESS (7 tables)
50. employee_surveys
51. survey_responses
52. recognition_categories
53. employee_recognitions
54. wellness_programs
55. wellness_program_enrollments
56. employee_feedback

## 14. ADDITIONAL SYSTEM TABLES (2 tables)
57. workflow_definitions
58. workflow_instances

TOTAL TABLES: 58

## IMPLEMENTATION STATUS IN WORKFLO-BACK:
✅ COMPLETED (All tables fully implemented with comprehensive models):

### Authentication & User Management (3/3)
- users, user_sessions, password_reset_tokens

### Organizational Structure (1/1)
- departments

### Employee Information (4/4)
- employee_profiles, salary_profiles, bank_profiles, emergency_contacts

### Attendance & Time Tracking (9/9)
- overtime_types, overtime_requests, overtime_records
- overtime_approval_workflows, overtime_budgets, overtime_calculations
- attendance_records, biostar_events, biostar_devices

### Leave Management (4/4)
- leave_types, leave_balances, leave_applications, company_holidays

### Payroll System (5/5)
- pay_cycles, payroll_records, payroll_adjustments, salary_adjustments
- employee_benefits

### Performance Management (3/3)
- performance_review_templates, performance_reviews, performance_goals

### Training & Development (5/5)
- training_modules, training_venues, employee_training_assignments
- training_sessions, training_session_participants

### Recruitment & Job Management (4/4)
- job_postings, job_applications, interview_schedules, candidate_evaluations

### Document Management (4/4)
- document_categories, employee_documents, company_documents, document_acknowledgments

### Notifications & Communication (3/3)
- notification_templates, notifications, email_logs

### Audit Logs & System Tracking (4/4)
- audit_logs, activity_logs, system_settings, company_info

### Employee Engagement & Wellness (7/7)
- employee_surveys, survey_responses, recognition_categories, employee_recognitions
- wellness_programs, wellness_program_enrollments, employee_feedback

### Workflow Management (2/2)
- workflow_definitions, workflow_instances

🎉 IMPLEMENTATION COMPLETE: 58/58 TABLES (100%)
